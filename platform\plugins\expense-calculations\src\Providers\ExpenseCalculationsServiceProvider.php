<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Providers;

use Bo<PERSON>ble\Base\Facades\DashboardMenu;
use Bo<PERSON>ble\Base\Supports\DashboardMenu as DashboardMenuSupport;
use Botble\Base\Traits\LoadAndPublishDataTrait;
use Bo<PERSON>ble\ExpenseCalculations\Models\Expense;
use Botble\ExpenseCalculations\Models\Income;
use Botble\ExpenseCalculations\Repositories\Eloquent\ExpenseRepository;
use Bo<PERSON>ble\ExpenseCalculations\Repositories\Eloquent\IncomeRepository;
use Botble\ExpenseCalculations\Repositories\Interfaces\ExpenseInterface;
use Botble\ExpenseCalculations\Repositories\Interfaces\IncomeInterface;
use Illuminate\Routing\Events\RouteMatched;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\ServiceProvider;

class ExpenseCalculationsServiceProvider extends ServiceProvider
{
    use LoadAndPublishDataTrait;

    public function register(): void
    {
        $this->app->bind(ExpenseInterface::class, function () {
            return new ExpenseRepository(new Expense());
        });

        $this->app->bind(IncomeInterface::class, function () {
            return new IncomeRepository(new Income());
        });
    }

    public function boot(): void
    {
        $this
            ->setNamespace('plugins/expense-calculations')
            ->loadHelpers()
            ->loadAndPublishConfigurations(['permissions'])
            ->loadMigrations()
            ->loadAndPublishTranslations()
            ->loadAndPublishViews()
            ->loadRoutes();

        $this->app['events']->listen(RouteMatched::class, function (): void {
            DashboardMenu::registerItem([
                'id' => 'cms-plugins-expense-calculations',
                'priority' => 5,
                'parent_id' => null,
                'name' => 'plugins/expense-calculations::expense-calculations.name',
                'icon' => 'ti ti-calculator',
                'url' => route('expense-calculations.dashboard'),
                'permissions' => ['expense-calculations.dashboard'],
            ])
                ->registerItem([
                    'id' => 'cms-plugins-expense-calculations-dashboard',
                    'priority' => 1,
                    'parent_id' => 'cms-plugins-expense-calculations',
                    'name' => 'plugins/expense-calculations::expense-calculations.dashboard',
                    'icon' => 'ti ti-chart-line',
                    'url' => route('expense-calculations.dashboard'),
                    'permissions' => ['expense-calculations.dashboard'],
                ])
                ->registerItem([
                    'id' => 'cms-plugins-expense-calculations-expenses',
                    'priority' => 2,
                    'parent_id' => 'cms-plugins-expense-calculations',
                    'name' => 'plugins/expense-calculations::expenses.name',
                    'icon' => 'ti ti-minus',
                    'url' => route('expense-calculations.expenses.index'),
                    'permissions' => ['expense-calculations.expenses.index'],
                ])
                ->registerItem([
                    'id' => 'cms-plugins-expense-calculations-income',
                    'priority' => 3,
                    'parent_id' => 'cms-plugins-expense-calculations',
                    'name' => 'plugins/expense-calculations::income.name',
                    'icon' => 'ti ti-plus',
                    'url' => route('expense-calculations.income.index'),
                    'permissions' => ['expense-calculations.income.index'],
                ]);
        });

        $this->app->register(HookServiceProvider::class);
    }
}
