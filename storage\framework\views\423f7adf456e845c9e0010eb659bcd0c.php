<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-body text-center">
                <h4 class="card-title"><?php echo e(SeoHelper::getTitle()); ?></h4>
                <p class="card-text mb-1"><?php echo BaseHelper::clean(__('Please enter the OTP code sent to :identifier.', ['identifier' => "<strong>$identifier</strong>"])); ?></p>
                <p class="card-text expiry-time">
                    <?php echo BaseHelper::clean(__('The OTP code will expire in :time.', ['time' => "<span></span>"])); ?>

                </p>

                <?php echo $form->renderForm(); ?>

            </div>
        </div>
    </div>
</div>

<?php if (isset($component)) { $__componentOriginald83dae5750a07af1a413e54a0071b325 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald83dae5750a07af1a413e54a0071b325 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => '8def1252668913628243c4d363bee1ef::form.index','data' => ['url' => route('otp.resend'),'method' => 'post','id' => 'resend-otp-form','style' => 'display: none;']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('core::form'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['url' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('otp.resend')),'method' => 'post','id' => 'resend-otp-form','style' => 'display: none;']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald83dae5750a07af1a413e54a0071b325)): ?>
<?php $attributes = $__attributesOriginald83dae5750a07af1a413e54a0071b325; ?>
<?php unset($__attributesOriginald83dae5750a07af1a413e54a0071b325); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald83dae5750a07af1a413e54a0071b325)): ?>
<?php $component = $__componentOriginald83dae5750a07af1a413e54a0071b325; ?>
<?php unset($__componentOriginald83dae5750a07af1a413e54a0071b325); ?>
<?php endif; ?>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        var expiryTime = '<?php echo e($expiryTime->getTimestamp()); ?>' * 1000;
        var element = document.querySelector('.expiry-time > span');

        function updateExpiryTime() {
            var now = new Date().getTime();
            var distance = expiryTime - now;

            if (distance < 0) {
                document.querySelector('.expiry-time').style.display = 'none';
            }

            var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            var seconds = Math.floor((distance % (1000 * 60)) / 1000);

            if (seconds < 10) {
                seconds = '0' + seconds;
            }

            element.style.fontWeight = 'bold';
            element.innerText = `${minutes}:${seconds}`;
        }

        setInterval(function () {
            updateExpiryTime();
        }, 1000);

        updateExpiryTime();
    });
</script>
<?php /**PATH D:\laragon\www\shofy\platform/plugins/sms/resources/views/phone-verification/verify.blade.php ENDPATH**/ ?>