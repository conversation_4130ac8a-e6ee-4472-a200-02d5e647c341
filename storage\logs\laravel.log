Array to string conversion (Connection: mysql, SQL: insert into `paygate_payments` (`token`, `gateway`, `payment_address`, `wallet_address`, `crypto_amount`, `fiat_amount`, `fiat_currency`, `tolerance_percentage`, `ipn_token`, `qr_code`, `callback_url`, `status`, `order_id`, `customer_id`, `customer_type`, `customer_email`, `api_response`, `updated_at`, `created_at`) values (gAqU6y56nVkGOaHpAZMB2Z8ytpS7cMhY, usdtpolygon, ******************************************, ******************************************, 3082.82, 3083.47, USD, 0.01, aHR0cHM6Ly9zaG9meS5nYy9wYXltZW50L3BheWdhdGUvY2FsbGJhY2svZ0FxVTZ5NTZuVmtHT2FIcEFaTUIyWjh5dHBTN2NNaFkvdXNkdHBvbHlnb24%3D, iVBORw0KGgoAAAANSUhEUgAAASwAAAEsCAIAAAD2HxkiAAAffElEQVR4nO3de5Bc1Z0f8N/vnHv7NW9p9Bp5JNADSWCQMEaSZUOws2Dj4LhMnLVxmcfi3Y2zbPzY2E4lu67FobKpylbZbBIcsos3XrwJ6wrgYFzrgmBWhkKRBwvQ+43QCA2S0Gie3T19H+eXP27PWEjTo76ae/s04vupqXWt6Ln39OM7p+/9nQeLCAGAPcp2AwDe6xBCAMsQQgDLEEIAyxBCAMsQQgDLEEIAy5xa/4GZG9mOizBthTORZteqndY6eHotSUrjWxir/hy3Jc3/gk+r1muCnhDAMoQQwDKEEMAyhBDAMoQQwDKEEMCymiWKaVmZ99T4O+mp1jlqiXXSRN6IuJWYWKy8hukdJK5YTx89IYBlCCGAZQghgGUIIYBlCCGAZQghgGUIIYBl8eqEtTR/USiRKlytg0z775deFe4SWx2zeT606AkBLEMIASxDCAEsQwgBLEMIASxDCAEsQwgBLEumTtg84hZ/YpX4Ui1CxhJr8UUrB0nEJVaZrAU9IYBlCCGAZQghgGUIIYBlCCGAZQghgGWXWomillQnFsVaNzFWS5K6R5/eZCsrM7YusdIFekIAyxBCAMsQQgDLEEIAyxBCAMsQQgDLEEIAy5KpEzZ/3aZ5pu0k0pJEVitsnqeT6nyoWprnQ4ueEMAyhBDAMoQQwDKEEMAyhBDAMoQQwDKEEMCyeHVCK/WcRKQ6i6/x8wmb5+CprjHZPNvIpQo9IYBlCCGAZQghgGUIIYBlCCGAZQghgGUIIYBlNeuEzTPbKhYrzU6vEpVIPbDW49NbczUuK3Msmwd6QgDLEEIAyxBCAMsQQgDLEEIAyxBCAMvS3RotkYlCqe6P1fiDpLpGoJV91+o/Y9onjdWSWFLdRg49IYBlCCGAZQghgGUIIYBlCCGAZQghgGUIIYBl/O4tT6W6RmCsg8z+yImcMSmpLkAY64yxxG1e40+KOiFAk0IIASxDCAEsQwgBLEMIASxDCAEsQwgBLKtZJ2yeolDjZ6Y1z8ZjSWmerdHqP3JSmmdrNNQJAZoUQghgGUIIYBlCCGAZQghgGUIIYFkyJYpU6xzNM+Uk1YM3flXCWlJ962PVOZqndJFqS9ATAliGEAJYhhACWIYQAliGEAJYhhACWIYQAlhWc2u09JZCTGSiUC21DtL4GVupeo9sPJbISZu/2IieEMAyhBDAMoQQwDKEEMAyhBDAMoQQwDKEEMCymnXCaVnZGi3V+YSJHDy9/dWS0viTNs/OcIl8aFMtbqMnBLAMIQSwDCEEsOxdvF12LM0z2PKCRMQQEZGebLNnzL6hoe2Dg9tPn94/NHR0bOx0uTzqeZUwDMNQa53Vuj2T6c7nl7a1rerqWtvdvXbu3NVdXRlV/SMbihCRSuIqq84jNM81YS2NX12p5oMRwga3ZAZR/Kayd6JU+oeBgWf7+7eePPn6yIinFClFIhSGJELG0FTjmUkpYiatiZmMyRizrKNj44IFtyxZ8tGenoWFQvTAUGSWUUQIL/ogCGGzhzAUieIXGPPz/v5HDxx47tix4ajZvk9BoKJ3i5mIxBh55zNiEVaKJh9jiMhxyHWJqFPkt3p777riiluXLHGUOvtcFwEhvOiDpBvCWKzMKmqesJ0vNEYrRURF3/+bAwce3r175+goMVOloo1hpYyIxHwCTMTMilmMCZWibJZErm5v//JVV919xRUtrnv2eWfjXbqqZdyTpvvJb6KmvPdCOPX904g8snfvn2/ffqhcJt9Xvs9EZrLfmyUWUcxCZFyXXHdFPv/NtWt/d80axdywb6czQwibpinvsRAaEcVMRC8MDHxr69ZfDQ+T5+kgEGaTTmsVM4uEjkOZzIbOzv+0ceONPT1nt+QiIISzPyNCaEdgjKPURBD8cV/fd3fsIK11pZJe/M5WjWI2S2H4R9dc8x/Wr885TtSeizgaQjj7MyKEFkSf+F2Dg/ds3rxtdJTHxxVz2Ng26Ojrbmvrde3tP7zppvfPnXtxOUQIZ39GhLDRos/6E4cO3fvii6NB4HheYK9tDnOQybQ7zl/fcMM/W7HiInKIEM7+jAhhQ0Wf8ge3b/96Xx/5vjamwR3g+TRRqBS57vfWr//a2rVxc4gQzv6MGLbWONHn+zt9fV/ftk17nhKxnkAiComUiPa8r2/b9p2+PkepwBjbjXpviTeVqalmFaU3zyWNbnMqgffv3u1MTITnVdstMiIs4kxM3L97NxH96fr1M/SHjZwVFeujYmV/vkQOjp6wEarfQl977f7du51yOQzD5klgRJjDMHTK5ft3737wtdfQHzZSvGvCZE5poye0OLAuGiP2k8OHb3/+eScIwiColcCG5bLW02MR7TiB4zz5sY99ZvnyaUe3pdETJrLTaC2pjlhM5kOIEM6+JTOI6uD7hoauf/LJku9TGNasBIpQw94LZqrx6ilm0rrgui/ffvvqrq7z6/gIYT0Hj3VGhDDFEAqREQmM2fD449tHR3WlMsOdGFepVtdtwJvBROO+79f+tqmJwmx2bXv7rz77WUcp9c7XCCGs5+CxzhjvxgzEYkQ08ze3bNleLjueF9R4mGYOK5WPrljx2C23BLOY31CPUMRh/p1f/OKn+/c7udy0F34hkeN528vlf7tly3dvuGE2Uy6gHghhWqLP7v87ceLBPXt0EIQX+rub0XpOLteYtuUcZ+avvqGILhYf3LPnn69c+aGFC5HDVOHuaFqYKBS5b/NmUaqerz7RdKXAVOctpSQ6/gVHqErUIKX+cPPmUAT5S1UySx7W0jx1xVgtn/0Zo5rED/bsebVUmvlS8JzzcsqFuPqPHxLpSuUVrX+4d++XrryyVuUw1Sv5RI7cPPcaakFPmDwR0UqVguCBvj72vGYrCdZPmNnzHujrKwWBVqrx9/DeIxDC5EXf3x7dt++oiAqCBsxOSokRUUFwVOTRfft4crUoSBxCmDytlG/MX2zfHqsbFKJQ5II/M0fa1HGEME6PFnWGf7F9u5/EWhgwLbysCQuMYaL/29+/r1xm36+/G3SV0swZpTTzDD8zT4FXM/7u1PEzdcfJiLDv7yuXnzt2jIkwli0NKFEkLLoi/8HevcyslDJ1fGqFiJR6q1h8pr8/rL3SBBOJMZ253MaFC6c9CBO9fPLkYLnMStWKflS6PDY+TnVf4ymlhPmRXbtuXbrUykYal7x05xNOe/Ck9tZIteWzOePb5fLyH/1oLAg41kBtYyic8TYqM3neuiVLXv3850XknGcUjS/70OOPbz18OFpbbaZDaU11d4YsIlq3Oc7hO++cl8+fc95E7j0m8tY3/91ROyNmmmdXplTv7J39NEXkuWPHxhxHe14Yqy6ilNJ6hgdo5pAoWq2wlhbX1ZmMzmRmvoliYl4WamPGHOe5Y8fuuOKKOn8rkUgkUgBo/sm+uCZM3jPHjjERx7yN0Zw3ZiKsFBM9098f8/egLrgmTJhvzJaBAYlzS6YWpupf2qjCrplZqZmHj2lmRymtFEcDZKbGvsyuJUZEfH/LW2/NMOwbLhpCmLB9Q0NvjI1RHUPDzsfM0VK8EvVXxpAxEu05IRIwU6UyVKnMcIThSiUoFoNoswoiiravUIqU0szRkU38VBoRCoI3xsb2Dw/HfVJwQQhhwl47fdp3HOX79XcZilkxB8aI74dRfpTiTKa7UJhfKMzP5+fl83Nyuc5MJq/1ss5OIjp/NmB0cfKvr7324JIlJZHhSmVoYuJUuRz9nC6Xw0qFjKluGuM4jlImzl8KReQ7zqtvv13304J6IYQJe+3tt0kprvPuPzMRGc8zQUC53Mq5c6+eO/eKzs6FhUJrJmNEir4/7vtnJibOVCqny+XhUunNUumLq1aRyLk5FCHmFwcGDpw+3VUotLju4tbW98+d2+q6ra6rmMd9/0SxuH94eOfg4MHh4aBYJMdRmQxFHd2FsAgp9RpCmAKEMGH7h4erCbnQJ1sxG98nY65fvPjTy5Zd3t5e9P3DIyN7zpx56siRgWJxZGKCgoCmLsOUIs9be9ll0x6tWic8derlQ4com33HbzlORy7X09KysqPjyjlzbl26tMVxXh8dfer1118+fpyUUq574Rwykwi+jqYBIUzY0bExMuaCPQszm0rl+p6eP9u0Keu6j+7e/fDOnW+OjFAYVr8xKsVKqWw2+p4pIpo5UKp1xhJFq+uqXM7JZsPJQmI0cWlkYmKkVNp74sRPRUjr93V03Lp06XdvvHHC9//dli0vDwxwNjvzhaIQkTFHR0djvh5wYTVD2PiiqhWJl31Pl0rVuym1n6ZmNp53++rVP7711p8dOXLrk08GYUiuqzIZNXnvJDpZ+M6FZy54FRc9IKpDnP2LrBRprVyXmY3Im+Pjf/Xaa/9j587HP/3pLZ/73Od+/vOf7N8/c3Uxukt0uly+4CvQAKkOAsFUpne9sSCgC60pKkTC/J2NGx3mbadOBeUyZTJa62oCJ6sLtX43MKbWzwy/NXVkxay1pkwmKJe3nTrlMN+/YYMwz/wHRpjJmLGg1hodcPEQwoRVZh56RkRRAVDk+zt2ENEDGzf+19tuW97WFnpeUCqFnifG8GTFz5kczx3dQWWijFKOUq7W0X+d+slo7SiVUYonb7dGvzh1kGjo6dRZVrS1PXTbbf9+wwYi+v6OHVTf9Pl6nh3EhWvChIXRRd3MjxFRmcx/e+WVN8fGvr1+/X1XX33f1Ve/NDDw0yNHXjh+fO/w8Ei5HAbBb2p9zKSUYSbmwyMjf/brX7dlMhmlXK1dpaJFK8phOO55B4aHRSk/+t2owBjdoWEmx+nM59d0dd3Y0/NPly3btGgREfWdPPlAX9/PDh5U2eyFpwuKhCjWp6DmAG5cE9b54HMe7zz0UFDfCqKK2VQq5Dg3Ll78uZUrP7VsWW9ra/SfDg4P7xsa2nvmzMGRkTfHx0+WSoMTE+O+Xw7Dsu9TpTLNIqVRG3K5vOvmtW513bm53MJCYXFr68rOziu7ulZ1da2MaoxEx8bGnj5y5McHD75w/DgFgcpm6yoYMjvMwX33xX1N6nn87CV1TRhLMvFBCM//x9m8nS0PP1yc6sQuRDOHIuT7FIaUza7s7Pzg/PkbFy78wLx5q7q65uXz5zzeN2akUjlTqVTCMDAmGko69c0zo3V3LteRzbrnDVs9VSrtHx5+5dSprSdPbjt16uDwMFUqpDW5brUN9WBucZzil7989r8hhPUf3M4sivegNscpGsO117o/W7QQhspkmDkw5uDg4MFTpx7buZOUItedm88vLBR6Wlp6WloWFgrzC4U5rru8q+uGnp5aB3zprbcOnDkz5Hkny+UTxeJAsThQKp0slQbLZfL9d4yYKRRk6j5qHaYmNBVjvBhQF4QwYd2Fwokg4LqnzE7VIZiIXXdqCIsRGSyVBsfHd0+OHY3mE161dOmOO+4w080n1MxffeGFbVPzCaO17ifHjqpstjpAZ3Ltw1jPi5USpbrz+ROxfg3qkMySh6nO1ktkflfDZqYtbWvbNT5+EV90pqoI1XMRkdas9dQihZo5dJyubFZNtzJidFO0K5t1CgU9eZfl7IkU59QYWYijHWik+vtC0eC06TERKbW0vX3XOf9uY8r1tAdPdZ5qIkXFWlCiSNiqzs56xqxd0FQmQ5GzK4Ezf3s8/8HnVB1ZSBkmInGMyQQm65u8Z/KeyfriBsKihNW01QoRYl41eWsHEoSvowlbN28e7dkjSeQwWSzEwsYNxTHk6zkj+Xmjhc5SNus7hmU8559pmzjVXpwoVIiJPEcJm7MK+FGxft28eRafwqUKIUzYuu5uNwh82804hxI22ogbdJ9pWX9wwdX93QuHWvKeq0RF8QyZPNeMFCpHFoy8suzErt5TJutzxaXJKr4hcoPgWoQwBQhhwlZ3dV3W1nawVGqeZX+VYZMLWsez/+SlFZv2L2orZzxHfG0mMqGhUHjy8lBUeyl33aGWDxxe3D9v5Nm1h3csP06BZqNYkXGcywoFfB1NA0KYMFepTT09h44eVTPsB9pAStjk/fe/Pu/OzVcsGCmMZ8PRvM/ERExS3StUou+qRIEWzwmEqedMx72/+GBf/6InP7RjIuM7JiOuu2nRovMrkDB7eE2T9/HeXommHdimhE3O/+irvV97+prOUnak4IsiJcwSrVwT3RTl6o0YZiJiYhZVccJizt9woPcP/v4jneMFoz0S/viSJTafzKULIUzeb/X2tgVBWPf8+pQowybn3/Rq7z2bV1UyoecYbaK3m4WrMzkmH8vVSobiqSgqw6OFyuIzHb//7IcK5ZZ8WLq5t9fSU7nE1fw6ml7pL6nFWNOrH87mjKHIvHz+5t7enwwM6ErF1rrxStjkgqsOd9/5wsrxXCBELFH2ojue1Z5QJgPJiklxpSTKIZ1TEpIQa8PFrNcz0vGFrR+Z+L3+7nw+NOfOsopbQEukxJfqyMdYLUzkw4meMGHRq/+lNWtEpJ418NPARKJN63j27l9eESoxLDR51SdExCxEMvVFlFk57FekPGJ63p9vnecUz4TCrDQLsRJdzPmr3lrw2/uvJaILTDqEi4IQJsxRSohuXrJkdT4vrjvz/i0pYWFxw0/+esnC4cKEa5QoouoypkTv+CLKzGKoeCZsX+Te9sCie/526V0/XLr+C11hIOVxw5qIyBgap5FdTwSD/T6rJhp8f8lACJMXGuMq9dW1ayWTsXFZyIFj5gy23LB/4Xgu0BJ1gL/pBqMHCTEpDjxhh2/4g3n3PHb5uts7iah9gfPJP1lw9yO9Kz7cUilWh7QpIpbCS/9rtJkmwFw6EMLkaWYhumv16qXMxnEa3RkKiRNuPDi/vZQJlFTLD9W7LdW7L6xZORz60rbQvet/Xv6PvjI/36EP/nL8kTuOPvXtE8PH/fddk/viQz2f+Ga37wkpDkUmwrE9m0tjg5hZnzyEMHnMHBpTcJxvr19voTNkUr5ee3SO7whP1iEmr+ZYOSr0pTQUVoom8GTO5dnuZdnxt4MnvnH8775y/MS+yqtPjfzlF/p/9dgwCa26sUW5yhgiUsKBqeT3v9QUCz1dYhDCVETrW9+zZs21hUKYzc602VJ8UhsRkzZzR3OLhlo8x7Dw5O1QYoeFqHgmKHS7H/3GwtW3dvgVCXwRQ2f6vR1Pj2bblFtQhS6nOGS2/WSUmLxy9U5q9X+YD/dNJPpUgMjKVKZUt6pKdRp1vNvuRJr5oZtu+vBTT1X3l5h9CyY3hzm/JdFGMVqYtJk/mi94bjkTcrUvZGKaGDVuQa+/d976L81rnecQ0dzlucMvjrMi5XCuXYuwEaKQdIbzHZqIWJEwMzExGaLABCeOnPt1NJEFBROZhZTq5yruQTCzvilEy0Z8aOHCr1155fcOH3aKxSCJd903ZnBiQmpM6vVMSEydxayqzoBgISKmIKAVH2vf9IcLFqzJE1H/y8UFa/LLNrUcfGE8+vUgINclpZkUexOmPGpISAxRdb4hi5ChYGwId2aShxCmSDGHIv9x06bnBwa2+76uVGZzWyMUIdd99fTplT/60bRpZqJiJaCs61YUEUd1CFZcKZolm9o/89BlRHTmjcqW//72zqdHFlxVWLAq5+QUEYmhTEGR4tKIMUTdl2c+cncnMWUKSqKqIxERC4lfsj8W79KDEKYoGpaS1frvbrnl+iefLDnO7Ed1B8YM1V4Gm0kTkalmh6IohiHlOrQIndhV/vHvvVEaMblOffpQ5eivS6tubicif0Lmrch+4t/Mf/6hwfkrMzfcOyffrkwgr/x0LPTJybMIRfdX2UFPmDyEMF1RZ7i6q+vRm266/fnnHRGpbw2oGcywBzALidBYzjdKaHKXGGIOA2Gm0lBQHg0Lc93AF51T2bbJicdMJpSeK7NffKi6hNS+Xxaf/6vhN/d42VYtUXmRiUgXWjFkJnm4O5o6zRwY85nly793/fVBLqe1nmXRQmr/EBEZHmyreI4hUtFtnOr/JVKKlauMTI4gZY6u+twcnzzkvfiDoUrRnDzkPfaNk3/7Ryff2u/l2rVMjS9lUsrtWIS/2snDa9oIjlKBMV9bt27E8+7fvdspl8MwnGV/OC1hoVCd7CgNFbyOUi7QhoiEmBVJKDrLgSdSNG6rNoZKI6GTU2KImAOfnv3Pg6/+bKw4YsYGTb5TC5Ex0Rg3ikKs2FlyVeJNBvSEjRLl8E/Xr7//qquCXE4plcZIGiFSRlVaKq8vGM2EanLqEoUBsebeD7b89veXzF+dGz8dGkM3/svuT/7JAlbEikiopUsPDQT+hBQ6lTFkTPWqMjqsEfLNxOoP5xJvM8Rbgfvdq0kqmYExjlIPbt/+9b4+8n1tTOLDwJSwyfrXHFx43zPrirkgqteT4uu+OHfD73RnW5UJ5JUnhhetyS++JkdER/pKz/2XwbcOeDqjzhplytGyF1E3KEoc1dLV69/3yHxd372ZS2wF7kQmWyWzDP67V5OEkCZz+MShQ/e++OJoEDiel0j98GxMpIi+9X/WLx5sr7ghEYvwxJiZszy76Xe7136mM3rY4BveL//yzK5nxg2Rm1dTo0yrOWSmahSJtOR1981fMdd/qqXeNiCE9bcEITxf2mMvohzuGhy8Z/PmbaOjPD6umBPsEqPO8OqDi+579trxnM+ihJkd9sriT8jlm1o23DX3xP6JLY8OlYZNrsMRJmMm4xft73tWN0hKtGpp7/H+1V8v0E69rw1CGKMlCOH5GjAAKsrhRBD8cV/fd3fsIK11pSLMSa0NpYRNxr/ruXWbDrxvpODpqGShmJSaGDdR6pyC0i6H5ux+b+qLKFXL/SzsOMpk7/5ey2Vrs8ZQnUs9IYQxWoIQnq8xoxCjDZWI6IWBgW9t3fqr4WHyPB0EiUSRhUhJNtBffXpD72D7eC5QRlXvx+pojUM2QtHehdV+b3Ky72Q3yMSiHJ3hOTfc691wR7sY4rpv5CGEMVqCEJ6vYUOBRcREW9iLPLJ3759v336oXCbfV77P0c4ts3gXWFicsGs8f9/fX79ouK1YCKqTkmhyhYuz7r5IdbYh/2YZUiZhlXPmXPdZ7+P/osOEonSMxiCEMVqCEJ6vwePxQ2O0UkRU9P2/OXDg4d27d46OEjNVKtoYVmrmXeynFU220KSM9lrKhbtf2rhqYP64jAoboeqq29WvnWflsPoviohJc4uQ/OPfz33k862x+sBqAxDC+luCEJ7PyqSYUCSajhQY8/P+/kcPHHju2LHhqCW+T0Ggoncr+sZozDmdJItEw9mixxgichxyXRZu18GnFi+5c+/anf97wuXWcjhmKBBW1e+i0Y8iZhYiI+zogqPznUu9j9+XX/aBXP3Xge9oD0JYf0sSmRCV6vOpJb0WzsZsnt3Ut9Po/z1RKv3DwMCz/f1bT558fWTEi3YaFKEw/M2W9JFoH8JoA1BmMiZjzLKOjo0LFtyypPdji963oCVPRKePeNt+PL7/haI35ghzKEFIoanuZOgo5TK7vkwsXJm57lOFdZ/IOhl1EX3gZItiRMJKflI9SLw/+ghhsmb/7KIo0llp9IzZNzS0fXBw++nT+4eGjo6NnS6XRz2vEoZhGGqts1q3ZzLd+fzStrZVXV1ru7vXzp27uqsrM9mFhUZYKLqoGzsVHt5afuPlyluvV8YGQ69sSHO+zenscXqvzq3cmFu6zlWKicgYURe7vBpCWP/BEcJGS/UFt3Lw2R/kPR5CjB0FsAwhBLAMIQSwDCEEsOxdMKm38Zf4idw6irtd0ewfPIP0qnNxz9j4JQ9TvetTS6yniZ4QwDKEEMAyhBDAMoQQwDKEEMAyhBDAMoQQwLKadcJEdp+KxUqJL72SoJXh0bEen+ocuVrSGwWeyBnjtiSRk6InBLAMIQSwDCEEsAwhBLAMIQSwDCEEsAwhBLAs3YWeUi3RpFpYa/wZGz/lz0phNr2D1NL8646iJwSwDCEEsAwhBLAMIQSwDCEEsAwhBLAsmalMqd4FvsQKA7WkWkRp/FYciSxUWUsi71rz1M/QEwJYhhACWIYQAliGEAJYhhACWIYQAliGEAJYFm8qUy2Nn1UUtyWpzluJdcZU1/yz8tqmp/HlTbKxCiZ6QgDLEEIAyxBCAMsQQgDLEEIAyxBCAMsQQgDLatYJ36WszMprfGUp1kFqSbWmmupBrOx2ll5L0BMCWIYQAliGEAJYhhACWIYQAliGEAJYVnPJQyuzSGKZ9u5wqvf0Uz1IImsBxjp4qgeJVXVI9V2I+9xjvRGJVGLQEwJYhhACWIYQAliGEAJYhhACWIYQAliGEAJYVrNOOC0r856avJRX6+CprkoY9+DpzZNqnqlwVlqSyLuMnhDAMoQQwDKEEMAyhBDAMoQQwDKEEMAyhBDAsnh1wlqaZ/W4RE4ad2ZaIgdJROP3qIt7kERa2PjJrqlWfdETAliGEAJYhhACWIYQAliGEAJYhhACWIYQAliWTJ2weSRSnbOyeGmTnzEp6W2Z1vzvWi3oCQEsQwgBLEMIASxDCAEsQwgBLEMIASy71EoUcaecJDKzJr3ZSUk9nfQKA3EPEouVyVax6h9Y8hDgUoAQAliGEAJYhhACWIYQAliGEAJYhhACWJZMnbB59sdKRPNMcrGybmLz70WXCCt7100LPSGAZQghgGUIIYBlCCGAZQghgGUIIYBlCCGAZfHqhM1TQEtKes8o1deqeVZ2TLXalmqZtPHl0FrNRk8IYBlCCGAZQghgGUIIYBlCCGAZQghgGV9is5AA3nXQEwJYhhACWIYQAliGEAJYhhACWIYQAliGEAJY9v8BUTsUQNKFQwAAAAAASUVORK5CYII=, https://shofy.gc/payment/paygate/callback/gAqU6y56nVkGOaHpAZMB2Z8ytpS7cMhY/usdtpolygon, pending, ?, ?, Botble\Ecommerce\Models\Customer, ?, {"conversion":{"status":"success","value_coin":"3082.82","exchange_rate":"0.999789"},"wallet":{"address_in":"******************************************","callback_url":"https:\/\/shofy.gc\/payment\/paygate\/callback\/gAqU6y56nVkGOaHpAZMB2Z8ytpS7cMhY\/usdtpolygon","ipn_token":"aHR0cHM6Ly9zaG9meS5nYy9wYXltZW50L3BheWdhdGUvY2FsbGJhY2svZ0FxVTZ5NTZuVmtHT2FIcEFaTUIyWjh5dHBTN2NNaFkvdXNkdHBvbHlnb24%3D"},"qr_code_status":"success","minimum_info":{"coin":"USDT","minimum":0.665,"prices":{"AED":"3.6737995271","AUD":"1.5261077410","BGN":"1.6612202260","BRL":"5.4229432895","CAD":"1.3615370454","CHF":"0.7945995211","CNY":"7.1668109396","COP":"3981.8673313508","CZK":"20.9284112227","DKK":"6.3357357286","EUR":"0.8491710261","GBP":"0.7324604206","HKD":"7.8512052267","HUF":"338.9614613948","IDR":"16204.9616774344","INR":"85.8198429289","JPY":"144.5027969257","LKR":"299.9981809788","MXN":"18.6344285948","MYR":"4.2218900780","NGN":"1530.2626169235","NOK":"10.0885269090","PHP":"56.5269212622","PLN":"3.6038077711","RON":"4.2958056613","RUB":"78.6223505209","SEK":"9.5630161185","SGD":"1.2736685206","THB":"32.3090825475","TRY":"39.8471017469","TWD":"28.9331008077","UAH":"41.7010986149","UGX":"3586.8008225998","USD":"1.0002108690","VND":"26175.**********","ZAR":"17.**********"}}}, 2025-07-05 22:19:27, 2025-07-05 22:19:27))
D:\laragon\www\shofy\vendor\laravel\framework\src\Illuminate\Database\Connection.php :822
[2025-07-10 16:00:37] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#6 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#9 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#10 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\shofy\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\shofy\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#48 {main}
"} 
[2025-07-20 00:33:52] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:83)
[stacktrace]
#0 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(81): tap(NULL, Object(Closure))
#2 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(64): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1010): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build(Object(Closure))
#6 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('encrypter', Array)
#9 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1202): Illuminate\\Foundation\\Application->make('encrypter')
#10 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1101): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1052): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('Illuminate\\\\Cook...')
#13 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('Illuminate\\\\Cook...', Array, true)
#14 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('Illuminate\\\\Cook...', Array)
#15 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('Illuminate\\\\Cook...', Array)
#16 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('Illuminate\\\\Cook...')
#17 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\laragon\\www\\shofy\\platform\\core\\js-validation\\src\\RemoteValidationMiddleware.php(43): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Botble\\JsValidation\\RemoteValidationMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#47 D:\\laragon\\www\\shofy\\public\\index.php(23): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#48 {main}
"} 
