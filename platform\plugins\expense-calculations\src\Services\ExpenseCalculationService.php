<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Services;

use Botble\Ecommerce\Enums\OrderStatusEnum;
use Bo<PERSON>ble\Ecommerce\Models\Order;
use Bo<PERSON>ble\Ecommerce\Models\OrderProduct;
use Botble\Ecommerce\Models\Product;
use Botble\ExpenseCalculations\Models\Expense;
use Botble\ExpenseCalculations\Models\Income;
use Botble\Payment\Enums\PaymentStatusEnum;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ExpenseCalculationService
{
    public function getTotalSalesCount($startDate = null, $endDate = null): float
    {
        try {
            $query = Order::query()
                ->where('status', OrderStatusEnum::COMPLETED)
                ->where('is_finished', true);

            if ($startDate && $endDate) {
                $query->whereBetween('created_at', [$startDate, $endDate]);
            }

            return $query->sum('amount') ?? 0;
        } catch (\Exception $e) {
            Log::error('Error calculating total sales count: ' . $e->getMessage());
            return 0;
        }
    }

    public function getTotalSaleOfTopSellingItems($startDate = null, $endDate = null): float
    {
        $query = OrderProduct::query()
            ->join('ec_orders', 'ec_orders.id', '=', 'ec_order_product.order_id')
            ->where('ec_orders.status', OrderStatusEnum::COMPLETED)
            ->where('ec_orders.is_finished', true);

        if ($startDate && $endDate) {
            $query->whereBetween('ec_orders.created_at', [$startDate, $endDate]);
        }

        // Get top 10 selling products by quantity
        $topProducts = $query->select('product_id', DB::raw('SUM(qty) as total_qty'))
            ->groupBy('product_id')
            ->orderBy('total_qty', 'desc')
            ->limit(10)
            ->pluck('product_id');

        if ($topProducts->isEmpty()) {
            return 0;
        }

        // Calculate total sales for these top products
        $topProductsSales = OrderProduct::query()
            ->join('ec_orders', 'ec_orders.id', '=', 'ec_order_product.order_id')
            ->where('ec_orders.status', OrderStatusEnum::COMPLETED)
            ->where('ec_orders.is_finished', true)
            ->whereIn('ec_order_product.product_id', $topProducts);

        if ($startDate && $endDate) {
            $topProductsSales->whereBetween('ec_orders.created_at', [$startDate, $endDate]);
        }

        return $topProductsSales->sum(DB::raw('ec_order_product.price * ec_order_product.qty')) ?? 0;
    }

    public function getTotalExpenses($startDate = null, $endDate = null): float
    {
        // Get product costs from completed orders
        $productCosts = $this->getProductCosts($startDate, $endDate);

        // Get manual expenses
        $manualExpenses = Expense::getTotalExpenses($startDate, $endDate);

        return $productCosts + $manualExpenses;
    }

    public function getTotalRevenue($startDate = null, $endDate = null): float
    {
        // Get sales revenue
        $salesRevenue = $this->getTotalSalesCount($startDate, $endDate);

        // Get manual income
        $manualIncome = Income::getTotalIncome($startDate, $endDate);

        return $salesRevenue + $manualIncome;
    }

    public function getNetProfit($startDate = null, $endDate = null): float
    {
        $revenue = $this->getTotalRevenue($startDate, $endDate);
        $expenses = $this->getTotalExpenses($startDate, $endDate);

        return $revenue - $expenses;
    }

    protected function getProductCosts($startDate = null, $endDate = null): float
    {
        $query = OrderProduct::query()
            ->join('ec_orders', 'ec_orders.id', '=', 'ec_order_product.order_id')
            ->join('ec_products', 'ec_products.id', '=', 'ec_order_product.product_id')
            ->where('ec_orders.status', OrderStatusEnum::COMPLETED)
            ->where('ec_orders.is_finished', true);

        if ($startDate && $endDate) {
            $query->whereBetween('ec_orders.created_at', [$startDate, $endDate]);
        }

        return $query->sum(DB::raw('COALESCE(ec_products.cost_per_item, 0) * ec_order_product.qty')) ?? 0;
    }

    public function getDashboardData($startDate = null, $endDate = null): array
    {
        return [
            'total_sales_count' => $this->getTotalSalesCount($startDate, $endDate),
            'total_sale_top_items' => $this->getTotalSaleOfTopSellingItems($startDate, $endDate),
            'total_expenses' => $this->getTotalExpenses($startDate, $endDate),
            'total_revenue' => $this->getTotalRevenue($startDate, $endDate),
            'net_profit' => $this->getNetProfit($startDate, $endDate),
        ];
    }
}
