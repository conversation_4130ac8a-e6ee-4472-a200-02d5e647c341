<?php

namespace Botble\ExpenseCalculations\Http\Requests;

use Botble\Base\Enums\BaseStatusEnum;
use Botble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class IncomeRequest extends Request
{
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'amount' => 'required|numeric|min:0',
            'source' => 'nullable|string|max:255',
            'income_date' => 'required|date',
            'reference_number' => 'nullable|string|max:255',
            'status' => Rule::in(BaseStatusEnum::values()),
        ];
    }

    public function attributes(): array
    {
        return [
            'title' => trans('plugins/expense-calculations::income.title'),
            'description' => trans('plugins/expense-calculations::income.description'),
            'amount' => trans('plugins/expense-calculations::income.amount'),
            'source' => trans('plugins/expense-calculations::income.source'),
            'income_date' => trans('plugins/expense-calculations::income.income_date'),
            'reference_number' => trans('plugins/expense-calculations::income.reference_number'),
            'status' => trans('core/base::tables.status'),
        ];
    }
}
