.expense-calculations-dashboard .card {
    transition: transform 0.2s ease-in-out;
}

.expense-calculations-dashboard .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.expense-calculations-widget .card {
    border: none;
    border-radius: 8px;
}

.expense-calculations-widget .card-body {
    padding: 1rem;
}

.expense-calculations-widget h4 {
    font-size: 1.25rem;
    font-weight: 600;
}

.expense-calculations-widget small {
    font-size: 0.75rem;
    opacity: 0.9;
}

.financial-metric {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    padding: 20px;
    color: white;
    text-align: center;
    margin-bottom: 20px;
}

.financial-metric.positive {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.financial-metric.negative {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.financial-metric .metric-icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
    opacity: 0.8;
}

.financial-metric .metric-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.financial-metric .metric-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

.date-filter-form {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.date-filter-form .form-control {
    border-radius: 6px;
}

@media (max-width: 768px) {
    .financial-metric {
        margin-bottom: 15px;
    }
    
    .financial-metric .metric-value {
        font-size: 1.5rem;
    }
    
    .date-filter-form {
        padding: 10px;
    }
}
