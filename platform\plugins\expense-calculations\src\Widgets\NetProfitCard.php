<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Widgets;

use Bo<PERSON>ble\Base\Widgets\Card;
use Bo<PERSON>ble\ExpenseCalculations\Services\ExpenseCalculationService;
use Botble\Ecommerce\Facades\EcommerceHelper;
use Illuminate\Http\Request;

class NetProfitCard extends Card
{
    protected ExpenseCalculationService $expenseCalculationService;

    public function __construct()
    {
        $this->expenseCalculationService = app('expense-calculation-service');
    }

    public function getOptions(): array
    {
        $request = app(Request::class);
        [$startDate, $endDate] = EcommerceHelper::getDateRangeInReport($request);

        $netProfit = $this->expenseCalculationService->getNetProfit($startDate, $endDate);

        return [
            'series' => [
                [
                    'data' => [$netProfit],
                ],
            ],
        ];
    }

    public function getViewData(): array
    {
        $request = app(Request::class);
        [$startDate, $endDate] = EcommerceHelper::getDateRangeInReport($request);

        $netProfit = $this->expenseCalculationService->getNetProfit($startDate, $endDate);

        return array_merge(parent::getViewData(), [
            'content' => view(
                'plugins/expense-calculations::reports.widgets.net-profit-card',
                [
                    'netProfit' => $netProfit,
                    'result' => 0, // You can calculate percentage change here
                ]
            )->render(),
        ]);
    }
}
