[2025-07-19 23:45:43] production.ERROR: Directory D:\laragon\www\shofy\storage\app/purifier/HTML not writable, please alter file permissions {"exception":"[object] (ErrorException(code: 0): Directory D:\\laragon\\www\\shofy\\storage\\app/purifier/HTML not writable, please alter file permissions at D:\\laragon\\www\\shofy\\vendor\\ezyang\\htmlpurifier\\library\\HTMLPurifier\\DefinitionCache\\Serializer.php:302)
[stacktrace]
#0 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(512, 'Directory D:\\\\la...', 'D:\\\\laragon\\\\www\\\\...', 302)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(512, 'Directory D:\\\\la...', 'D:\\\\laragon\\\\www\\\\...', 302)
#2 D:\\laragon\\www\\shofy\\vendor\\ezyang\\htmlpurifier\\library\\HTMLPurifier\\DefinitionCache\\Serializer.php(302): trigger_error('Directory D:\\\\la...', 512)
#3 D:\\laragon\\www\\shofy\\vendor\\ezyang\\htmlpurifier\\library\\HTMLPurifier\\DefinitionCache\\Serializer.php(251): HTMLPurifier_DefinitionCache_Serializer->_testPermissions('D:\\\\laragon\\\\www\\\\...', 493)
#4 D:\\laragon\\www\\shofy\\vendor\\ezyang\\htmlpurifier\\library\\HTMLPurifier\\DefinitionCache\\Serializer.php(20): HTMLPurifier_DefinitionCache_Serializer->_prepareDir(Object(HTMLPurifier_Config))
#5 D:\\laragon\\www\\shofy\\vendor\\ezyang\\htmlpurifier\\library\\HTMLPurifier\\DefinitionCache\\Decorator.php(52): HTMLPurifier_DefinitionCache_Serializer->add(Object(HTMLPurifier_HTMLDefinition), Object(HTMLPurifier_Config))
#6 D:\\laragon\\www\\shofy\\vendor\\ezyang\\htmlpurifier\\library\\HTMLPurifier\\DefinitionCache\\Decorator\\Cleanup.php(29): HTMLPurifier_DefinitionCache_Decorator->add(Object(HTMLPurifier_HTMLDefinition), Object(HTMLPurifier_Config))
#7 D:\\laragon\\www\\shofy\\vendor\\ezyang\\htmlpurifier\\library\\HTMLPurifier\\Config.php(499): HTMLPurifier_DefinitionCache_Decorator_Cleanup->add(Object(HTMLPurifier_HTMLDefinition), Object(HTMLPurifier_Config))
#8 D:\\laragon\\www\\shofy\\vendor\\ezyang\\htmlpurifier\\library\\HTMLPurifier\\Config.php(415): HTMLPurifier_Config->getDefinition('HTML', false, false)
#9 D:\\laragon\\www\\shofy\\vendor\\ezyang\\htmlpurifier\\library\\HTMLPurifier\\Generator.php(74): HTMLPurifier_Config->getHTMLDefinition()
#10 D:\\laragon\\www\\shofy\\vendor\\ezyang\\htmlpurifier\\library\\HTMLPurifier.php(158): HTMLPurifier_Generator->__construct(Object(HTMLPurifier_Config), Object(HTMLPurifier_Context))
#11 D:\\laragon\\www\\shofy\\vendor\\mews\\purifier\\src\\Purifier.php(290): HTMLPurifier->purify('en', Object(HTMLPurifier_Config))
#12 D:\\laragon\\www\\shofy\\vendor\\mews\\purifier\\src\\helpers.php(6): Mews\\Purifier\\Purifier->clean('en', NULL)
#13 D:\\laragon\\www\\shofy\\platform\\core\\base\\src\\Helpers\\BaseHelper.php(330): clean('en', NULL)
#14 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Base\\Helpers\\BaseHelper->clean('en')
#15 D:\\laragon\\www\\shofy\\platform\\core\\base\\src\\Casts\\SafeContent.php(21): Illuminate\\Support\\Facades\\Facade::__callStatic('clean', Array)
#16 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(899): Botble\\Base\\Casts\\SafeContent->get(Object(Botble\\Language\\Models\\Language), 'lang_locale', 'en', Array)
#17 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(875): Illuminate\\Database\\Eloquent\\Model->getClassCastableAttributeValue('lang_locale', 'en')
#18 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(2336): Illuminate\\Database\\Eloquent\\Model->castAttribute('lang_locale', 'en')
#19 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(528): Illuminate\\Database\\Eloquent\\Model->transformModelValue('lang_locale', 'en')
#20 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\HasAttributes.php(482): Illuminate\\Database\\Eloquent\\Model->getAttributeValue('lang_locale')
#21 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2384): Illuminate\\Database\\Eloquent\\Model->getAttribute('lang_locale')
#22 D:\\laragon\\www\\shofy\\platform\\core\\base\\src\\Models\\BaseModel.php(28): Illuminate\\Database\\Eloquent\\Model->__get('lang_locale')
#23 D:\\laragon\\www\\shofy\\platform\\plugins\\language\\src\\LanguageManager.php(107): Botble\\Base\\Models\\BaseModel->__get('lang_locale')
#24 D:\\laragon\\www\\shofy\\platform\\plugins\\language\\src\\LanguageManager.php(1004): Botble\\Language\\LanguageManager->getSupportedLocales()
#25 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Language\\LanguageManager->setLocale()
#26 D:\\laragon\\www\\shofy\\platform\\plugins\\language\\src\\Providers\\LanguageServiceProvider.php(132): Illuminate\\Support\\Facades\\Facade::__callStatic('setLocale', Array)
#27 [internal function]: Botble\\Language\\Providers\\LanguageServiceProvider->addLanguageMiddlewareToPublicRoute(Array)
#28 D:\\laragon\\www\\shofy\\platform\\core\\base\\src\\Supports\\Filter.php(25): call_user_func_array(Array, Array)
#29 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Base\\Supports\\Filter->fire('group_public_ro...', Array)
#30 D:\\laragon\\www\\shofy\\platform\\core\\base\\helpers\\action-filter.php(39): Illuminate\\Support\\Facades\\Facade::__callStatic('fire', Array)
#31 D:\\laragon\\www\\shofy\\platform\\packages\\theme\\src\\Theme.php(876): apply_filters('group_public_ro...', Array)
#32 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(510): Botble\\Theme\\Theme->Botble\\Theme\\{closure}(Object(Illuminate\\Routing\\Router))
#33 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(466): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#34 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#35 D:\\laragon\\www\\shofy\\platform\\packages\\theme\\src\\Theme.php(875): Illuminate\\Support\\Facades\\Facade::__callStatic('group', Array)
#36 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Botble\\Theme\\Theme->registerRoutes(Object(Closure))
#37 D:\\laragon\\www\\shofy\\platform\\plugins\\ads\\routes\\web.php(28): Illuminate\\Support\\Facades\\Facade::__callStatic('registerRoutes', Array)
#38 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(510): Illuminate\\Support\\ServiceProvider->{closure}(Object(Illuminate\\Routing\\Router))
#39 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(466): Illuminate\\Routing\\Router->loadRoutes(Object(Closure))
#40 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Routing\\Router->group(Array, Object(Closure))
#41 D:\\laragon\\www\\shofy\\platform\\plugins\\ads\\routes\\web.php(7): Illuminate\\Support\\Facades\\Facade::__callStatic('group', Array)
#42 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\ServiceProvider.php(194): require('D:\\\\laragon\\\\www\\\\...')
#43 D:\\laragon\\www\\shofy\\platform\\core\\base\\src\\Traits\\LoadAndPublishDataTrait.php(84): Illuminate\\Support\\ServiceProvider->loadRoutesFrom('D:\\\\laragon\\\\www\\\\...')
#44 D:\\laragon\\www\\shofy\\platform\\plugins\\ads\\src\\Providers\\AdsServiceProvider.php(47): Botble\\Ads\\Providers\\AdsServiceProvider->loadRoutes(Array)
#45 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Botble\\Ads\\Providers\\AdsServiceProvider->boot()
#46 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#47 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#48 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#49 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#50 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1150): Illuminate\\Container\\Container->call(Array)
#51 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): Illuminate\\Foundation\\Application->bootProvider(Object(Botble\\Ads\\Providers\\AdsServiceProvider))
#52 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Botble\\Ads\\Providers\\AdsServiceProvider), 'Botble\\\\Ads\\\\Prov...')
#53 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1130): array_walk(Array, Object(Closure))
#54 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#55 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(341): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#56 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(473): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#57 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(195): Illuminate\\Foundation\\Console\\Kernel->bootstrap()
#58 Command line code(1): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#59 {main}
"} 
