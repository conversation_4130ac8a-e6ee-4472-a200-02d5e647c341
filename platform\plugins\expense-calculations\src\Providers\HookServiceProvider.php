<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Providers;

use Botble\Base\Facades\Assets;
use Bo<PERSON>ble\Base\Supports\ServiceProvider;
use <PERSON><PERSON>ble\Dashboard\Events\RenderingDashboardWidgets;
use Bo<PERSON>ble\Dashboard\Supports\DashboardWidgetInstance;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;

class HookServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->app['events']->listen(RenderingDashboardWidgets::class, function (): void {
            add_filter(DASHBOARD_FILTER_ADMIN_LIST, [$this, 'registerDashboardWidgets'], 210, 2);
        });
    }

    public function registerDashboardWidgets(array $widgets, Collection $widgetSettings): array
    {
        if (! Auth::user()->hasPermission('expense-calculations.dashboard')) {
            return $widgets;
        }

        Assets::addScriptsDirectly(['vendor/core/plugins/expense-calculations/js/dashboard-widgets.js']);

        return (new DashboardWidgetInstance())
            ->setPermission('expense-calculations.dashboard')
            ->setKey('widget_expense_calculations_dashboard')
            ->setTitle(trans('plugins/expense-calculations::expense-calculations.dashboard_widget_title'))
            ->setIcon('ti ti-calculator')
            ->setColor('#28a745')
            ->setRoute(route('expense-calculations.dashboard-widget'))
            ->setBodyClass('scroll-table')
            ->setColumn('col-md-12 col-sm-12')
            ->init($widgets, $widgetSettings);
    }
}
