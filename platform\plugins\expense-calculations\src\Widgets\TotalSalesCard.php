<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Widgets;

use Bo<PERSON>ble\Base\Widgets\Card;
use Bo<PERSON>ble\ExpenseCalculations\Services\ExpenseCalculationService;
use Botble\Ecommerce\Facades\EcommerceHelper;
use Illuminate\Http\Request;

class TotalSalesCard extends Card
{
    protected ExpenseCalculationService $expenseCalculationService;

    public function __construct()
    {
        $this->expenseCalculationService = app('expense-calculation-service');
    }

    public function getOptions(): array
    {
        $request = app(Request::class);
        [$startDate, $endDate] = EcommerceHelper::getDateRangeInReport($request);

        $totalSales = $this->expenseCalculationService->getTotalSalesCount($startDate, $endDate);

        return [
            'series' => [
                [
                    'data' => [$totalSales],
                ],
            ],
        ];
    }

    public function getViewData(): array
    {
        $request = app(Request::class);
        [$startDate, $endDate] = EcommerceHelper::getDateRangeInReport($request);

        $totalSales = $this->expenseCalculationService->getTotalSalesCount($startDate, $endDate);

        return array_merge(parent::getViewData(), [
            'content' => view(
                'plugins/expense-calculations::reports.widgets.total-sales-card',
                [
                    'totalSales' => $totalSales,
                    'result' => 0, // You can calculate percentage change here
                ]
            )->render(),
        ]);
    }
}
