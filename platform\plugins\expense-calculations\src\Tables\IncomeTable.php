<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Tables;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\ExpenseCalculations\Models\Income;
use Bo<PERSON>ble\ExpenseCalculations\Repositories\Interfaces\IncomeInterface;
use Bo<PERSON>ble\Table\Abstracts\TableAbstract;
use Bo<PERSON>ble\Table\Actions\DeleteAction;
use Botble\Table\Actions\EditAction;
use Botble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\BulkChanges\CreatedAtBulkChange;
use Botble\Table\BulkChanges\NameBulkChange;
use Botble\Table\BulkChanges\StatusBulkChange;
use Botble\Table\Columns\Column;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\NameColumn;
use Botble\Table\Columns\StatusColumn;
use Illuminate\Contracts\Routing\UrlGenerator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;

class IncomeTable extends TableAbstract
{
    public function __construct(IncomeInterface $repository, UrlGenerator $urlGenerator)
    {
        parent::__construct($repository, $urlGenerator);

        $this->hasActions = true;
        $this->hasFilter = true;
        $this->hasCheckbox = true;
        $this->hasBulkActions = true;
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('title', function (Income $item) {
                return anchor_link(route('expense-calculations.income.edit', $item->getKey()), $item->title);
            })
            ->editColumn('amount', function (Income $item) {
                return $item->amount_format;
            })
            ->editColumn('income_date', function (Income $item) {
                return $item->income_date->format('Y-m-d');
            });

        return $this->toJson($data);
    }

    public function query(): Relation|Builder|QueryBuilder
    {
        $query = $this->repository->getModel()
            ->select([
                'id',
                'title',
                'amount',
                'source',
                'income_date',
                'status',
                'created_at',
            ]);

        return $this->applyScopes($query);
    }

    public function columns(): array
    {
        return [
            IdColumn::make(),
            NameColumn::make('title')->route('expense-calculations.income.edit'),
            Column::make('amount')
                ->title(trans('plugins/expense-calculations::income.amount'))
                ->alignEnd(),
            Column::make('source')
                ->title(trans('plugins/expense-calculations::income.source')),
            Column::make('income_date')
                ->title(trans('plugins/expense-calculations::income.income_date')),
            StatusColumn::make(),
            CreatedAtColumn::make(),
        ];
    }

    public function buttons(): array
    {
        return $this->addCreateButton(route('expense-calculations.income.create'), 'expense-calculations.income.create');
    }

    public function bulkActions(): array
    {
        return [
            DeleteBulkAction::make()->permission('expense-calculations.income.destroy'),
        ];
    }

    public function getBulkChanges(): array
    {
        return [
            NameBulkChange::make(),
            StatusBulkChange::make(),
            CreatedAtBulkChange::make(),
        ];
    }

    public function getFilters(): array
    {
        return $this->getBulkChanges();
    }

    public function actions(): array
    {
        return [
            EditAction::make()->route('expense-calculations.income.edit'),
            DeleteAction::make()->route('expense-calculations.income.destroy'),
        ];
    }
}
