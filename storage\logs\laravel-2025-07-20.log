[2025-07-20 00:05:56] production.ERROR: Command "cms:plugin:publish" is not defined.

Did you mean one of these?
    cms:plugin:activate
    cms:plugin:activate:all
    cms:plugin:assets:publish
    cms:plugin:clear-compiled
    cms:plugin:deactivate
    cms:plugin:deactivate:all
    cms:plugin:discover
    cms:plugin:install-from-marketplace
    cms:plugin:list
    cms:plugin:remove
    cms:plugin:remove:all
    cms:plugin:update-version-info {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"cms:plugin:publish\" is not defined.

Did you mean one of these?
    cms:plugin:activate
    cms:plugin:activate:all
    cms:plugin:assets:publish
    cms:plugin:clear-compiled
    cms:plugin:deactivate
    cms:plugin:deactivate:all
    cms:plugin:discover
    cms:plugin:install-from-marketplace
    cms:plugin:list
    cms:plugin:remove
    cms:plugin:remove:all
    cms:plugin:update-version-info at D:\\laragon\\www\\shofy\\vendor\\symfony\\console\\Application.php:725)
[stacktrace]
#0 D:\\laragon\\www\\shofy\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('cms:plugin:publ...')
#1 D:\\laragon\\www\\shofy\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\laragon\\www\\shofy\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\laragon\\www\\shofy\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
