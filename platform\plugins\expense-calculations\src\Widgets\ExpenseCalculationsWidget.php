<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Widgets;

use Bo<PERSON>ble\Base\Widgets\Contracts\AdminWidget;
use Botble\ExpenseCalculations\Services\ExpenseCalculationService;
use Botble\Ecommerce\Facades\EcommerceHelper;
use Illuminate\Http\Request;

class ExpenseCalculationsWidget implements AdminWidget
{
    protected ExpenseCalculationService $expenseCalculationService;

    public function __construct()
    {
        $this->expenseCalculationService = app('expense-calculation-service');
    }

    public function render(string $screenName): string
    {
        $request = app(Request::class);
        [$startDate, $endDate] = EcommerceHelper::getDateRangeInReport($request);

        $data = $this->expenseCalculationService->getDashboardData($startDate, $endDate);

        return view('plugins/expense-calculations::reports.widgets.general', compact('data'))->render();
    }
}
