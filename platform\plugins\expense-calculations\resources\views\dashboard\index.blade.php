@extends(BaseHelper::getAdminMasterLayoutTemplate())

@section('content')
    <div class="row">
        <div class="col-md-12">
            <x-core::card>
                <x-core::card.header>
                    <x-core::card.title>
                        {{ trans('plugins/expense-calculations::expense-calculations.dashboard') }}
                    </x-core::card.title>
                    <div class="card-actions">
                        <form method="GET" class="d-flex align-items-center">
                            <div class="me-2">
                                <input type="date" name="start_date" class="form-control form-control-sm" 
                                       value="{{ $startDate->format('Y-m-d') }}" />
                            </div>
                            <div class="me-2">
                                <input type="date" name="end_date" class="form-control form-control-sm" 
                                       value="{{ $endDate->format('Y-m-d') }}" />
                            </div>
                            <button type="submit" class="btn btn-primary btn-sm">
                                {{ trans('plugins/expense-calculations::expense-calculations.filter') }}
                            </button>
                        </form>
                    </div>
                </x-core::card.header>
                <x-core::card.body>
                    <div class="row">
                        <!-- Total Sales Count -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <x-core::card class="h-100">
                                <x-core::card.body class="text-center">
                                    <div class="mb-3">
                                        <x-core::icon name="ti ti-shopping-cart" class="text-primary" size="xl" />
                                    </div>
                                    <h3 class="mb-1">{{ format_price($data['total_sales_count']) }}</h3>
                                    <p class="text-muted mb-0">{{ trans('plugins/expense-calculations::expense-calculations.total_sales_count') }}</p>
                                </x-core::card.body>
                            </x-core::card>
                        </div>

                        <!-- Total Sale of Top Selling Items -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <x-core::card class="h-100">
                                <x-core::card.body class="text-center">
                                    <div class="mb-3">
                                        <x-core::icon name="ti ti-trending-up" class="text-success" size="xl" />
                                    </div>
                                    <h3 class="mb-1">{{ format_price($data['total_sale_top_items']) }}</h3>
                                    <p class="text-muted mb-0">{{ trans('plugins/expense-calculations::expense-calculations.total_sale_top_items') }}</p>
                                </x-core::card.body>
                            </x-core::card>
                        </div>

                        <!-- Total Expenses -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <x-core::card class="h-100">
                                <x-core::card.body class="text-center">
                                    <div class="mb-3">
                                        <x-core::icon name="ti ti-minus" class="text-danger" size="xl" />
                                    </div>
                                    <h3 class="mb-1">{{ format_price($data['total_expenses']) }}</h3>
                                    <p class="text-muted mb-0">{{ trans('plugins/expense-calculations::expense-calculations.total_expenses') }}</p>
                                </x-core::card.body>
                            </x-core::card>
                        </div>

                        <!-- Total Revenue -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <x-core::card class="h-100">
                                <x-core::card.body class="text-center">
                                    <div class="mb-3">
                                        <x-core::icon name="ti ti-plus" class="text-info" size="xl" />
                                    </div>
                                    <h3 class="mb-1">{{ format_price($data['total_revenue']) }}</h3>
                                    <p class="text-muted mb-0">{{ trans('plugins/expense-calculations::expense-calculations.total_revenue') }}</p>
                                </x-core::card.body>
                            </x-core::card>
                        </div>

                        <!-- Net Profit -->
                        <div class="col-md-6 col-lg-4 mb-4">
                            <x-core::card class="h-100">
                                <x-core::card.body class="text-center">
                                    <div class="mb-3">
                                        <x-core::icon name="ti ti-calculator" class="{{ $data['net_profit'] >= 0 ? 'text-success' : 'text-danger' }}" size="xl" />
                                    </div>
                                    <h3 class="mb-1 {{ $data['net_profit'] >= 0 ? 'text-success' : 'text-danger' }}">
                                        {{ format_price($data['net_profit']) }}
                                    </h3>
                                    <p class="text-muted mb-0">{{ trans('plugins/expense-calculations::expense-calculations.net_profit') }}</p>
                                </x-core::card.body>
                            </x-core::card>
                        </div>
                    </div>
                </x-core::card.body>
            </x-core::card>
        </div>
    </div>
@endsection
