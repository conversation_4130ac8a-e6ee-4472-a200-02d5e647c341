<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Http\Controllers;

use Botble\Base\Events\BeforeEditContentEvent;
use Bo<PERSON>ble\Base\Events\CreatedContentEvent;
use Botble\Base\Events\DeletedContentEvent;
use Botble\Base\Events\UpdatedContentEvent;
use Botble\Base\Facades\PageTitle;
use Botble\Base\Forms\FormBuilder;
use Botble\Base\Http\Controllers\BaseController;
use Botble\Base\Http\Responses\BaseHttpResponse;
use Botble\ExpenseCalculations\Forms\IncomeForm;
use Botble\ExpenseCalculations\Http\Requests\IncomeRequest;
use Botble\ExpenseCalculations\Models\Income;
use Botble\ExpenseCalculations\Repositories\Interfaces\IncomeInterface;
use Bo<PERSON>ble\ExpenseCalculations\Tables\IncomeTable;
use Exception;
use Illuminate\Http\Request;

class IncomeController extends BaseController
{
    public function __construct(protected IncomeInterface $incomeRepository)
    {
    }

    public function index(IncomeTable $table)
    {
        PageTitle::setTitle(trans('plugins/expense-calculations::income.name'));

        return $table->renderTable();
    }

    public function create(FormBuilder $formBuilder)
    {
        PageTitle::setTitle(trans('plugins/expense-calculations::income.create'));

        return $formBuilder->create(IncomeForm::class)->renderForm();
    }

    public function store(IncomeRequest $request, BaseHttpResponse $response)
    {
        $income = $this->incomeRepository->createOrUpdate($request->input());

        event(new CreatedContentEvent(INCOME_MODULE_SCREEN_NAME, $request, $income));

        return $response
            ->setPreviousUrl(route('expense-calculations.income.index'))
            ->setMessage(trans('core/base::notices.create_success_message'));
    }

    public function show(int|string $id, Request $request)
    {
        $income = $this->incomeRepository->findOrFail($id);

        event(new BeforeEditContentEvent($request, $income));

        PageTitle::setTitle(trans('plugins/expense-calculations::income.edit') . ' "' . $income->title . '"');

        return view('plugins/expense-calculations::income.show', compact('income'));
    }

    public function edit(int|string $id, FormBuilder $formBuilder, Request $request)
    {
        $income = $this->incomeRepository->findOrFail($id);

        event(new BeforeEditContentEvent($request, $income));

        PageTitle::setTitle(trans('plugins/expense-calculations::income.edit') . ' "' . $income->title . '"');

        return $formBuilder->create(IncomeForm::class, ['model' => $income])->renderForm();
    }

    public function update(int|string $id, IncomeRequest $request, BaseHttpResponse $response)
    {
        $income = $this->incomeRepository->findOrFail($id);

        $income->fill($request->input());

        $income = $this->incomeRepository->createOrUpdate($income);

        event(new UpdatedContentEvent(INCOME_MODULE_SCREEN_NAME, $request, $income));

        return $response
            ->setPreviousUrl(route('expense-calculations.income.index'))
            ->setMessage(trans('core/base::notices.update_success_message'));
    }

    public function destroy(int|string $id, Request $request, BaseHttpResponse $response)
    {
        try {
            $income = $this->incomeRepository->findOrFail($id);

            $this->incomeRepository->delete($income);

            event(new DeletedContentEvent(INCOME_MODULE_SCREEN_NAME, $request, $income));

            return $response->setMessage(trans('core/base::notices.delete_success_message'));
        } catch (Exception $exception) {
            return $response
                ->setError()
                ->setMessage($exception->getMessage());
        }
    }
}
