<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Widgets;

use Botble\Base\Widgets\Card;
use Bo<PERSON>ble\ExpenseCalculations\Services\ExpenseCalculationService;
use Botble\Ecommerce\Facades\EcommerceHelper;
use Illuminate\Http\Request;

class TotalExpensesCard extends Card
{
    protected ExpenseCalculationService $expenseCalculationService;

    public function __construct()
    {
        $this->expenseCalculationService = app('expense-calculation-service');
    }

    public function getOptions(): array
    {
        $request = app(Request::class);
        [$startDate, $endDate] = EcommerceHelper::getDateRangeInReport($request);

        $totalExpenses = $this->expenseCalculationService->getTotalExpenses($startDate, $endDate);

        return [
            'series' => [
                [
                    'data' => [$totalExpenses],
                ],
            ],
        ];
    }

    public function getViewData(): array
    {
        $request = app(Request::class);
        [$startDate, $endDate] = EcommerceHelper::getDateRangeInReport($request);

        $totalExpenses = $this->expenseCalculationService->getTotalExpenses($startDate, $endDate);

        return [
            'totalExpenses' => $totalExpenses,
            'result' => 0, // You can calculate percentage change here
        ];
    }
}
