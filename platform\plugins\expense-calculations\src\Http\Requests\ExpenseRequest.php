<?php

namespace Botble\ExpenseCalculations\Http\Requests;

use Botble\Base\Enums\BaseStatusEnum;
use Botble\Support\Http\Requests\Request;
use Illuminate\Validation\Rule;

class ExpenseRequest extends Request
{
    public function rules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'amount' => 'required|numeric|min:0',
            'category' => 'nullable|string|max:255',
            'expense_date' => 'required|date',
            'reference_number' => 'nullable|string|max:255',
            'status' => Rule::in(BaseStatusEnum::values()),
        ];
    }

    public function attributes(): array
    {
        return [
            'title' => trans('plugins/expense-calculations::expenses.title'),
            'description' => trans('plugins/expense-calculations::expenses.description'),
            'amount' => trans('plugins/expense-calculations::expenses.amount'),
            'category' => trans('plugins/expense-calculations::expenses.category'),
            'expense_date' => trans('plugins/expense-calculations::expenses.expense_date'),
            'reference_number' => trans('plugins/expense-calculations::expenses.reference_number'),
            'status' => trans('core/base::tables.status'),
        ];
    }
}
