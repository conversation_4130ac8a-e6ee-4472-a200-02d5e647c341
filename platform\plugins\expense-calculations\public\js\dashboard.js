class ExpenseCalculationsDashboard {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.initDatePickers();
        this.loadCharts();
    }

    bindEvents() {
        // Quick date range buttons
        $(document).on('click', '.quick-date-btn', (e) => {
            e.preventDefault();
            const range = $(e.target).data('range');
            this.setDateRange(range);
        });

        // Auto-submit form on date change
        $(document).on('change', 'input[name="start_date"], input[name="end_date"]', () => {
            this.submitDateFilter();
        });
    }

    initDatePickers() {
        // Initialize date pickers if needed
        if (typeof flatpickr !== 'undefined') {
            flatpickr('input[type="date"]', {
                dateFormat: 'Y-m-d',
                allowInput: true
            });
        }
    }

    setDateRange(range) {
        const today = new Date();
        let startDate, endDate;

        switch (range) {
            case 'today':
                startDate = endDate = today;
                break;
            case 'yesterday':
                startDate = endDate = new Date(today.getTime() - 24 * 60 * 60 * 1000);
                break;
            case 'this_week':
                startDate = new Date(today.setDate(today.getDate() - today.getDay()));
                endDate = new Date();
                break;
            case 'last_week':
                const lastWeekStart = new Date(today.setDate(today.getDate() - today.getDay() - 7));
                startDate = lastWeekStart;
                endDate = new Date(lastWeekStart.getTime() + 6 * 24 * 60 * 60 * 1000);
                break;
            case 'this_month':
                startDate = new Date(today.getFullYear(), today.getMonth(), 1);
                endDate = new Date();
                break;
            case 'last_month':
                startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                endDate = new Date(today.getFullYear(), today.getMonth(), 0);
                break;
            case 'this_year':
                startDate = new Date(today.getFullYear(), 0, 1);
                endDate = new Date();
                break;
            case 'last_year':
                startDate = new Date(today.getFullYear() - 1, 0, 1);
                endDate = new Date(today.getFullYear() - 1, 11, 31);
                break;
            default:
                return;
        }

        // Format dates as YYYY-MM-DD
        const formatDate = (date) => {
            return date.toISOString().split('T')[0];
        };

        $('input[name="start_date"]').val(formatDate(startDate));
        $('input[name="end_date"]').val(formatDate(endDate));

        this.submitDateFilter();
    }

    submitDateFilter() {
        const form = $('form').first();
        if (form.length) {
            form.submit();
        }
    }

    loadCharts() {
        // Placeholder for future chart implementations
        // Could integrate Chart.js or similar library here
        console.log('Charts would be loaded here');
    }

    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    }

    showNotification(message, type = 'success') {
        if (typeof Botble !== 'undefined' && Botble.showNotification) {
            Botble.showNotification(message, type);
        } else {
            alert(message);
        }
    }
}

// Widget-specific functionality
class ExpenseCalculationsWidget {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.animateCounters();
    }

    bindEvents() {
        // Refresh widget data
        $(document).on('click', '.widget-refresh', (e) => {
            e.preventDefault();
            this.refreshWidget();
        });
    }

    animateCounters() {
        // Animate number counters
        $('.metric-value').each(function() {
            const $this = $(this);
            const countTo = $this.text().replace(/[^0-9.-]+/g, '');
            
            if (countTo && !isNaN(countTo)) {
                $({ countNum: 0 }).animate({
                    countNum: countTo
                }, {
                    duration: 2000,
                    easing: 'linear',
                    step: function() {
                        $this.text(Math.floor(this.countNum));
                    },
                    complete: function() {
                        $this.text(countTo);
                    }
                });
            }
        });
    }

    refreshWidget() {
        // Refresh widget data via AJAX
        const widgetUrl = $('.widget-item').data('url');
        if (widgetUrl) {
            $.get(widgetUrl)
                .done((response) => {
                    if (response.data) {
                        $('.widget-item .card-body').html(response.data);
                        this.animateCounters();
                    }
                })
                .fail(() => {
                    this.showNotification('Failed to refresh widget data', 'error');
                });
        }
    }

    showNotification(message, type = 'success') {
        if (typeof Botble !== 'undefined' && Botble.showNotification) {
            Botble.showNotification(message, type);
        }
    }
}

// Initialize when document is ready
$(document).ready(function() {
    // Initialize dashboard if on dashboard page
    if ($('.expense-calculations-dashboard').length) {
        new ExpenseCalculationsDashboard();
    }

    // Initialize widget if widget is present
    if ($('.expense-calculations-widget').length) {
        new ExpenseCalculationsWidget();
    }
});
