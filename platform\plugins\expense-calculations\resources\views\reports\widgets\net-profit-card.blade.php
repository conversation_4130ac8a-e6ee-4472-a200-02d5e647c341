<x-core::card class="analytic-card">
    <x-core::card.body class="p-3">
        <div class="row align-items-center">
            <div class="col-auto">
                <x-core::icon
                    class="text-white {{ $netProfit >= 0 ? 'bg-green' : 'bg-red' }} rounded p-1"
                    name="ti ti-calculator"
                    size="md"
                />
            </div>
            <div class="col mt-0">
                <p class="text-secondary mb-0 fs-4">
                    {{ trans('plugins/expense-calculations::expense-calculations.net_profit') }}
                </p>
                <h3 class="mb-n1 fs-1 {{ $netProfit >= 0 ? 'text-success' : 'text-danger' }}">
                    {{ format_price($netProfit) }}
                </h3>
            </div>
        </div>
    </x-core::card.body>
    @include('plugins/expense-calculations::reports.widgets.card-description')
</x-core::card>
