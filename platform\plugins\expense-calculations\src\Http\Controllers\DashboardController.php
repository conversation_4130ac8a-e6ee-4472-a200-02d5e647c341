<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Http\Controllers;

use Botble\Base\Facades\Assets;
use Botble\Base\Http\Controllers\BaseController;
use Botble\ExpenseCalculations\Services\ExpenseCalculationService;
use Carbon\Carbon;
use Illuminate\Http\Request;

class DashboardController extends BaseController
{
    public function __construct(protected ExpenseCalculationService $expenseCalculationService)
    {
    }

    public function index(Request $request)
    {
        $this->pageTitle(trans('plugins/expense-calculations::expense-calculations.dashboard'));

        Assets::addScriptsDirectly([
            'vendor/core/plugins/expense-calculations/js/dashboard.js',
        ])
            ->addStylesDirectly([
                'vendor/core/plugins/expense-calculations/css/dashboard.css',
            ]);

        $startDate = $request->get('start_date') ? Carbon::parse($request->get('start_date')) : Carbon::now()->startOfMonth();
        $endDate = $request->get('end_date') ? Carbon::parse($request->get('end_date')) : Carbon::now()->endOfMonth();

        $data = $this->expenseCalculationService->getDashboardData($startDate, $endDate);

        return view('plugins/expense-calculations::dashboard.index', compact('data', 'startDate', 'endDate'));
    }

    public function widget(Request $request)
    {
        $startDate = Carbon::now()->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();

        $data = $this->expenseCalculationService->getDashboardData($startDate, $endDate);

        return $this
            ->httpResponse()
            ->setData(
                view('plugins/expense-calculations::dashboard.widget', compact('data'))->render()
            );
    }
}
