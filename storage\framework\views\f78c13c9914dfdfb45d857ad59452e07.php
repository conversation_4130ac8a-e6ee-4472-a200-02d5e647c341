<form action="<?php echo e(! empty($route) ? route($route, $order->id) : route('orders.edit', $order->id)); ?>">
    <?php if (isset($component)) { $__componentOriginal98af68e526ddc4187878063a3b54ba78 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal98af68e526ddc4187878063a3b54ba78 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => '8def1252668913628243c4d363bee1ef::form.textarea','data' => ['label' => (trans('plugins/ecommerce::order.note') . ' ' . trans('plugins/ecommerce::order.note_description')),'name' => 'description','placeholder' => trans('plugins/ecommerce::order.add_note'),'helperText' => trans('plugins/ecommerce::order.add_note_helper'),'value' => $order->description,'class' => 'textarea-auto-height']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('core::form.textarea'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute((trans('plugins/ecommerce::order.note') . ' ' . trans('plugins/ecommerce::order.note_description'))),'name' => 'description','placeholder' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('plugins/ecommerce::order.add_note')),'helper-text' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('plugins/ecommerce::order.add_note_helper')),'value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($order->description),'class' => 'textarea-auto-height']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal98af68e526ddc4187878063a3b54ba78)): ?>
<?php $attributes = $__attributesOriginal98af68e526ddc4187878063a3b54ba78; ?>
<?php unset($__attributesOriginal98af68e526ddc4187878063a3b54ba78); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal98af68e526ddc4187878063a3b54ba78)): ?>
<?php $component = $__componentOriginal98af68e526ddc4187878063a3b54ba78; ?>
<?php unset($__componentOriginal98af68e526ddc4187878063a3b54ba78); ?>
<?php endif; ?>

    <?php if (isset($component)) { $__componentOriginal98af68e526ddc4187878063a3b54ba78 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal98af68e526ddc4187878063a3b54ba78 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => '8def1252668913628243c4d363bee1ef::form.textarea','data' => ['label' => trans('plugins/ecommerce::order.admin_private_notes'),'name' => 'private_notes','placeholder' => trans('plugins/ecommerce::order.add_note'),'helperText' => trans('plugins/ecommerce::order.admin_private_notes_helper'),'value' => $order->private_notes,'class' => 'textarea-auto-height']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('core::form.textarea'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('plugins/ecommerce::order.admin_private_notes')),'name' => 'private_notes','placeholder' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('plugins/ecommerce::order.add_note')),'helper-text' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(trans('plugins/ecommerce::order.admin_private_notes_helper')),'value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($order->private_notes),'class' => 'textarea-auto-height']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal98af68e526ddc4187878063a3b54ba78)): ?>
<?php $attributes = $__attributesOriginal98af68e526ddc4187878063a3b54ba78; ?>
<?php unset($__attributesOriginal98af68e526ddc4187878063a3b54ba78); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal98af68e526ddc4187878063a3b54ba78)): ?>
<?php $component = $__componentOriginal98af68e526ddc4187878063a3b54ba78; ?>
<?php unset($__componentOriginal98af68e526ddc4187878063a3b54ba78); ?>
<?php endif; ?>

    <?php if (isset($component)) { $__componentOriginal922f7d3260a518f4cf606eecf9669dcb = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal922f7d3260a518f4cf606eecf9669dcb = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => '8def1252668913628243c4d363bee1ef::button','data' => ['type' => 'button','class' => 'btn-update-order']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('core::button'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'button','class' => 'btn-update-order']); ?>
        <?php echo e(trans('plugins/ecommerce::order.save')); ?>

     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal922f7d3260a518f4cf606eecf9669dcb)): ?>
<?php $attributes = $__attributesOriginal922f7d3260a518f4cf606eecf9669dcb; ?>
<?php unset($__attributesOriginal922f7d3260a518f4cf606eecf9669dcb); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal922f7d3260a518f4cf606eecf9669dcb)): ?>
<?php $component = $__componentOriginal922f7d3260a518f4cf606eecf9669dcb; ?>
<?php unset($__componentOriginal922f7d3260a518f4cf606eecf9669dcb); ?>
<?php endif; ?>
</form>
<?php /**PATH D:\laragon\www\shofy\platform/plugins/ecommerce/resources/views/orders/edit/form-edit.blade.php ENDPATH**/ ?>