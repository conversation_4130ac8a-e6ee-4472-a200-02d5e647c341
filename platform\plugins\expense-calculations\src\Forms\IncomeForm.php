<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Forms;

use Botble\Base\Enums\BaseStatusEnum;
use Botble\Base\Forms\FieldOptions\NameFieldOption;
use Botble\Base\Forms\FieldOptions\SelectFieldOption;
use Botble\Base\Forms\FieldOptions\StatusFieldOption;
use Botble\Base\Forms\FieldOptions\TextareaFieldOption;
use Botble\Base\Forms\FieldOptions\TextFieldOption;
use Botble\Base\Forms\Fields\DateField;
use Botble\Base\Forms\Fields\NumberField;
use Botble\Base\Forms\Fields\SelectField;
use Botble\Base\Forms\Fields\TextareaField;
use Botble\Base\Forms\Fields\TextField;
use Botble\Base\Forms\FormAbstract;
use Bo<PERSON>ble\ExpenseCalculations\Http\Requests\IncomeRequest;
use Botble\ExpenseCalculations\Models\Income;

class IncomeForm extends FormAbstract
{
    public function setup(): void
    {
        $this
            ->setupModel(new Income())
            ->setValidatorClass(IncomeRequest::class)
            ->withCustomFields()
            ->add('title', TextField::class, NameFieldOption::make()->required()->toArray())
            ->add('description', TextareaField::class, TextareaFieldOption::make()
                ->label(trans('plugins/expense-calculations::income.description'))
                ->placeholder(trans('plugins/expense-calculations::income.description_placeholder'))
                ->toArray())
            ->add('amount', NumberField::class, [
                'label' => trans('plugins/expense-calculations::income.amount'),
                'required' => true,
                'attr' => [
                    'step' => '0.01',
                    'min' => '0',
                    'placeholder' => '0.00',
                ],
            ])
            ->add('source', TextField::class, TextFieldOption::make()
                ->label(trans('plugins/expense-calculations::income.source'))
                ->placeholder(trans('plugins/expense-calculations::income.source_placeholder'))
                ->toArray())
            ->add('income_date', DateField::class, [
                'label' => trans('plugins/expense-calculations::income.income_date'),
                'required' => true,
                'default_value' => now()->format('Y-m-d'),
            ])
            ->add('reference_number', TextField::class, TextFieldOption::make()
                ->label(trans('plugins/expense-calculations::income.reference_number'))
                ->placeholder(trans('plugins/expense-calculations::income.reference_number_placeholder'))
                ->toArray())
            ->add('status', SelectField::class, StatusFieldOption::make()->toArray())
            ->setBreakFieldPoint('status');
    }
}
