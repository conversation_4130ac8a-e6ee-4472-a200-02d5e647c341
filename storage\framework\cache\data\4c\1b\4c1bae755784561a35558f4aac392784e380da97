1753058707O:42:"Illuminate\Pagination\LengthAwarePaginator":12:{s:8:" * items";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:4:{i:0;O:29:"Botble\Contact\Models\Contact":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"contacts";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:2;s:4:"name";s:15:"<PERSON>";s:5:"email";s:26:"<EMAIL>";s:5:"phone";s:14:"(*************";s:10:"created_at";s:19:"2024-04-26 08:15:03";}s:11:" * original";a:5:{s:2:"id";i:2;s:4:"name";s:15:"<PERSON>.";s:5:"email";s:26:"<EMAIL>";s:5:"phone";s:14:"(*************";s:10:"created_at";s:19:"2024-04-26 08:15:03";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:6:{s:6:"status";s:38:"Botble\Contact\Enums\ContactStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:7:"address";s:29:"Botble\Base\Casts\SafeContent";s:7:"subject";s:29:"Botble\Base\Casts\SafeContent";s:7:"content";s:29:"Botble\Base\Casts\SafeContent";s:13:"custom_fields";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:7:"address";i:4;s:7:"subject";i:5;s:7:"content";i:6;s:6:"status";i:7;s:13:"custom_fields";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:1;O:29:"Botble\Contact\Models\Contact":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"contacts";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:5;s:4:"name";s:15:"August Schmeler";s:5:"email";s:22:"<EMAIL>";s:5:"phone";s:14:"**************";s:10:"created_at";s:19:"2024-04-26 08:15:03";}s:11:" * original";a:5:{s:2:"id";i:5;s:4:"name";s:15:"August Schmeler";s:5:"email";s:22:"<EMAIL>";s:5:"phone";s:14:"**************";s:10:"created_at";s:19:"2024-04-26 08:15:03";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:6:{s:6:"status";s:38:"Botble\Contact\Enums\ContactStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:7:"address";s:29:"Botble\Base\Casts\SafeContent";s:7:"subject";s:29:"Botble\Base\Casts\SafeContent";s:7:"content";s:29:"Botble\Base\Casts\SafeContent";s:13:"custom_fields";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:7:"address";i:4;s:7:"subject";i:5;s:7:"content";i:6;s:6:"status";i:7;s:13:"custom_fields";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:2;O:29:"Botble\Contact\Models\Contact":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"contacts";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:6;s:4:"name";s:15:"Tracey Turcotte";s:5:"email";s:19:"<EMAIL>";s:5:"phone";s:17:"+****************";s:10:"created_at";s:19:"2024-04-26 08:15:03";}s:11:" * original";a:5:{s:2:"id";i:6;s:4:"name";s:15:"Tracey Turcotte";s:5:"email";s:19:"<EMAIL>";s:5:"phone";s:17:"+****************";s:10:"created_at";s:19:"2024-04-26 08:15:03";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:6:{s:6:"status";s:38:"Botble\Contact\Enums\ContactStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:7:"address";s:29:"Botble\Base\Casts\SafeContent";s:7:"subject";s:29:"Botble\Base\Casts\SafeContent";s:7:"content";s:29:"Botble\Base\Casts\SafeContent";s:13:"custom_fields";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:7:"address";i:4;s:7:"subject";i:5;s:7:"content";i:6;s:6:"status";i:7;s:13:"custom_fields";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}i:3;O:29:"Botble\Contact\Models\Contact":33:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"contacts";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:7;s:4:"name";s:18:"Elliott Harvey DDS";s:5:"email";s:19:"<EMAIL>";s:5:"phone";s:12:"************";s:10:"created_at";s:19:"2024-04-26 08:15:03";}s:11:" * original";a:5:{s:2:"id";i:7;s:4:"name";s:18:"Elliott Harvey DDS";s:5:"email";s:19:"<EMAIL>";s:5:"phone";s:12:"************";s:10:"created_at";s:19:"2024-04-26 08:15:03";}s:10:" * changes";a:0:{}s:11:" * previous";a:0:{}s:8:" * casts";a:6:{s:6:"status";s:38:"Botble\Contact\Enums\ContactStatusEnum";s:4:"name";s:29:"Botble\Base\Casts\SafeContent";s:7:"address";s:29:"Botble\Base\Casts\SafeContent";s:7:"subject";s:29:"Botble\Base\Casts\SafeContent";s:7:"content";s:29:"Botble\Base\Casts\SafeContent";s:13:"custom_fields";s:5:"array";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:27:" * relationAutoloadCallback";N;s:26:" * relationAutoloadContext";N;s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:8:{i:0;s:4:"name";i:1;s:5:"email";i:2;s:5:"phone";i:3;s:7:"address";i:4;s:7:"subject";i:5;s:7:"content";i:6;s:6:"status";i:7;s:13:"custom_fields";}s:10:" * guarded";a:1:{i:0;s:1:"*";}}}s:28:" * escapeWhenCastingToString";b:0;}s:10:" * perPage";i:10;s:14:" * currentPage";i:1;s:7:" * path";s:57:"https://shofy.gc/admin/expense-calculations/income/create";s:8:" * query";a:0:{}s:11:" * fragment";N;s:11:" * pageName";s:4:"page";s:28:" * escapeWhenCastingToString";b:0;s:10:"onEachSide";i:3;s:10:" * options";a:2:{s:4:"path";s:57:"https://shofy.gc/admin/expense-calculations/income/create";s:8:"pageName";s:4:"page";}s:8:" * total";i:4;s:11:" * lastPage";i:1;}