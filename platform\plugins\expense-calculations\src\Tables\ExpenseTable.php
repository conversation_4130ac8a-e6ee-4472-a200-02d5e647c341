<?php

namespace Bo<PERSON>ble\ExpenseCalculations\Tables;

use Bo<PERSON>ble\Base\Enums\BaseStatusEnum;
use Bo<PERSON>ble\ExpenseCalculations\Models\Expense;
use Botble\ExpenseCalculations\Repositories\Interfaces\ExpenseInterface;
use Bo<PERSON>ble\Table\Abstracts\TableAbstract;
use Botble\Table\Actions\DeleteAction;
use Bo<PERSON>ble\Table\Actions\EditAction;
use Bo<PERSON>ble\Table\BulkActions\DeleteBulkAction;
use Botble\Table\BulkChanges\CreatedAtBulkChange;
use Botble\Table\BulkChanges\NameBulkChange;
use Botble\Table\BulkChanges\StatusBulkChange;
use Botble\Table\Columns\Column;
use Botble\Table\Columns\CreatedAtColumn;
use Botble\Table\Columns\IdColumn;
use Botble\Table\Columns\NameColumn;
use Botble\Table\Columns\StatusColumn;
use Illuminate\Contracts\Routing\UrlGenerator;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Database\Query\Builder as QueryBuilder;
use Illuminate\Http\JsonResponse;

class ExpenseTable extends TableAbstract
{
    public function __construct(ExpenseInterface $repository, UrlGenerator $urlGenerator)
    {
        parent::__construct($repository, $urlGenerator);

        $this->hasActions = true;
        $this->hasFilter = true;
        $this->hasCheckbox = true;
        $this->hasBulkActions = true;
    }

    public function ajax(): JsonResponse
    {
        $data = $this->table
            ->eloquent($this->query())
            ->editColumn('title', function (Expense $item) {
                return anchor_link(route('expense-calculations.expenses.edit', $item->getKey()), $item->title);
            })
            ->editColumn('amount', function (Expense $item) {
                return $item->amount_format;
            })
            ->editColumn('expense_date', function (Expense $item) {
                return $item->expense_date->format('Y-m-d');
            });

        return $this->toJson($data);
    }

    public function query(): Relation|Builder|QueryBuilder
    {
        $query = $this->repository->getModel()
            ->select([
                'id',
                'title',
                'amount',
                'category',
                'expense_date',
                'status',
                'created_at',
            ]);

        return $this->applyScopes($query);
    }

    public function columns(): array
    {
        return [
            IdColumn::make(),
            NameColumn::make('title')->route('expense-calculations.expenses.edit'),
            Column::make('amount')
                ->title(trans('plugins/expense-calculations::expenses.amount'))
                ->alignEnd(),
            Column::make('category')
                ->title(trans('plugins/expense-calculations::expenses.category')),
            Column::make('expense_date')
                ->title(trans('plugins/expense-calculations::expenses.expense_date')),
            StatusColumn::make(),
            CreatedAtColumn::make(),
        ];
    }

    public function buttons(): array
    {
        return $this->addCreateButton(route('expense-calculations.expenses.create'), 'expense-calculations.expenses.create');
    }

    public function bulkActions(): array
    {
        return [
            DeleteBulkAction::make()->permission('expense-calculations.expenses.destroy'),
        ];
    }

    public function getBulkChanges(): array
    {
        return [
            NameBulkChange::make(),
            StatusBulkChange::make(),
            CreatedAtBulkChange::make(),
        ];
    }

    public function getFilters(): array
    {
        return $this->getBulkChanges();
    }

    public function actions(): array
    {
        return [
            EditAction::make()->route('expense-calculations.expenses.edit'),
            DeleteAction::make()->route('expense-calculations.expenses.destroy'),
        ];
    }
}
